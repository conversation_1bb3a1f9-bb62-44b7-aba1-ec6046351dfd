package com.challanty.android.kp3.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.input.pointer.pointerInput

@Composable
fun SolvedGameBoard(
    modifier: Modifier = Modifier,
    onNewGameClick: () -> Unit,
    solvedImage: ImageBitmap,
    tintColor: Color, // Target tint color
    backgroundColor: Color, // Target background color
) {
    var targetBGColor by remember { mutableStateOf(Color.Transparent) }
    var targetTintColor by remember { mutableStateOf(Color.Transparent) }

    val animatedBGColor by animateColorAsState(
        targetValue = targetBGColor,
        animationSpec = tween(durationMillis = 2000),
        label = "Background Color Animation"
    )

    val animatedTintColor by animateColorAsState(
        targetValue = targetTintColor,
        animationSpec = tween(durationMillis = 2000),
        label = "Tint Color Animation"
    )

    // Use LaunchedEffect to trigger the color change once
    LaunchedEffect(Unit) {
        targetBGColor = backgroundColor
        targetTintColor = tintColor
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(animatedBGColor)
            .pointerInput(null) { detectTapGestures(onTap = { onNewGameClick() }) },
        contentAlignment = Alignment.Center
    ) {
        // Display the solved image with animated tint
        Image(
            bitmap = solvedImage,
            // TODO i18n
            contentDescription = "Solved Game Board",
            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Screen) // Interesting
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Color) // Interesting
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Difference) // Interesting
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Exclusion) // Interesting
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Hue) // Interesting
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Modulate) // Interesting
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Saturation) // Interesting
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.DstOut) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Dst) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.DstIn) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.DstAtop) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.DstOver) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Darken) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Clear) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Hardlight) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Softlight) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Lighten) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Luminosity) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Multiply) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Overlay) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Plus) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Src) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.SrcAtop) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.SrcIn) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.SrcOut) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.SrcOver) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.ColorBurn) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.ColorDodge) // X
//            colorFilter = ColorFilter.tint(animatedTintColor, BlendMode.Xor) // X
        )
    }
}