package com.challanty.android.kp3.pictureUnits.original

/**
 * Factory class for creating Celtic picture unit strategies.
 */
object CelticPicUnitFactory {

    fun createPicUnit(picUnitType: Int): CelticPicUnitStrategy {
//        Note: using only CelticPicUnitB in a puzzle produces an optical illusion!!
//        return CelticPicUnitB()
        return when (picUnitType % 26) {
            0 -> CelticPicUnitA()
            1 -> CelticPicUnitB()
            2 -> CelticPicUnitC()
            3 -> CelticPicUnitD()
            4 -> CelticPicUnitE()
            5 -> CelticPicUnitF()
            6 -> CelticPicUnitG()
            7 -> CelticPicUnitH()
            8 -> CelticPicUnitI()
            9 -> CelticPicUnitJ()
            10 -> CelticPicUnitK()
            11 -> CelticPicUnitL()
            12 -> CelticPicUnitM()
            13 -> CelticPicUnitN()
            14 -> CelticPicUnitO()
            15 -> CelticPicUnitP()
            16 -> CelticPicUnitQ()
            17 -> CelticPicUnitR()
            18 -> CelticPicUnitS()
            19 -> CelticPicUnitT()
            20 -> CelticPicUnitU()
            21 -> CelticPicUnitV()
            22 -> CelticPicUnitW()
            23 -> CelticPicUnitX()
            24 -> CelticPicUnitY()
            25 -> CelticPicUnitZ()
            else -> CelticPicUnitA() // Default fallback
        }
    }
}