package com.challanty.android.kp3.ui.screens

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.Slider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.challanty.android.kp3.R
import com.challanty.android.kp3.navigation.NavRoutes
import com.challanty.android.kp3.ui.components.DropdownSelector
import com.challanty.android.kp3.viewModel.SettingsViewModel

/**
 * Settings screen.
 * This screen allows the user to configure game settings.
 *
 * @param onNavigate Callback for when a navigation item is clicked.
 * @param viewModel The ViewModel for this screen.
 * @param onBackPressed Callback for when the back button is pressed.
 */
@Composable
fun SettingsScreen(
    onNavigate: (String) -> Unit,
    viewModel: SettingsViewModel = hiltViewModel(),
    onBackPressed: () -> Unit = {},
) {
    // Handle back button press to exit the app
    BackHandler {
        onBackPressed()
    }

    // Apply changes when leaving the screen
    DisposableEffect(Unit) {
        onDispose {
            viewModel.applyPendingChanges()
        }
    }

    val validTileRowValues by viewModel.settingsValidTileRowValues.collectAsState()
    val validTileColumnValues by viewModel.settingsValidTileColValues.collectAsState()

    val validBoardRowValues by viewModel.settingsValidBoardRowValues.collectAsState()
    val validBoardColumnValues by viewModel.settingsValidBoardColValues.collectAsState()

    // Get current values as known by the Settings ViewModel (may not reflect the repository yet)
    val boardRows by viewModel.settingsBoardRows.collectAsState()
    val boardCols by viewModel.settingsBoardCols.collectAsState()
    val tileRows by viewModel.settingsTileRows.collectAsState()
    val tileCols by viewModel.settingsTileCols.collectAsState()
    // TODO add lock percent to screen
    val lockPercent by viewModel.settingsLockPercent.collectAsState()
    val tilesRotatable by viewModel.settingsTilesRotatable.collectAsState()
    val canRotateTiles by viewModel.settingsCanRotateTiles.collectAsState()
    val animate by viewModel.settingsAnimate.collectAsState()
    val animateRotation by viewModel.settingsAnimateRotation.collectAsState()
    val animateSwap by viewModel.settingsAnimateSwap.collectAsState()

    BaseScreen(
        title = stringResource(R.string.screen_title_settings),
        currentRoute = NavRoutes.Settings.route,
        onNavigate = onNavigate
    ) {
        // Use a scrollable column for the content
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Add padding inside the scrollable content
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(R.string.settings_title),
                    style = MaterialTheme.typography.headlineMedium,
                    modifier = Modifier.padding(bottom = 24.dp)
                )

                // Game Board Settings Card
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.settings_board_dimensions),
                            style = MaterialTheme.typography.titleMedium,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )

                        // Board Rows Dropdown
                        DropdownSelector(
                            label = stringResource(R.string.settings_board_rows, boardRows),
                            selectedValue = boardRows,
                            options = validBoardRowValues,
                            onValueSelected = { newValue ->
                                viewModel.setPendingBoardRows(newValue)
                            },
                            modifier = Modifier.fillMaxWidth()
                        )

                        // Board Columns Dropdown
                        DropdownSelector(
                            label = stringResource(R.string.settings_board_cols, boardCols),
                            selectedValue = boardCols,
                            options = validBoardColumnValues,
                            onValueSelected = { newValue ->
                                viewModel.setPendingBoardCols(newValue)
                            },
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // Tile Settings Card
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.settings_tile_dimensions),
                            style = MaterialTheme.typography.titleMedium,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )

                        // Tile Rows Dropdown
                        DropdownSelector(
                            label = stringResource(R.string.settings_tile_rows, tileRows),
                            selectedValue = tileRows,
                            options = validTileRowValues,
                            onValueSelected = { newValue ->
                                viewModel.setPendingTileRows(newValue)
                            },
                            modifier = Modifier.fillMaxWidth()
                        )

                        // Tile Columns Dropdown
                        DropdownSelector(
                            label = stringResource(R.string.settings_tile_columns, tileCols),
                            selectedValue = tileCols,
                            options = validTileColumnValues,
                            onValueSelected = { newValue ->
                                viewModel.setPendingTileCols(newValue)
                            },
                            modifier = Modifier.fillMaxWidth()
                        )

                        // Constraint explanation
                        if (boardRows % 2 == 1 || boardCols % 2 == 1) {
                            Text(
                                text = stringResource(R.string.settings_constraint_explanation),
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.secondary,
                                modifier = Modifier.padding(top = 8.dp)
                            )
                        }

                        Spacer(modifier = Modifier.height(16.dp))

                        // Rotatable Tiles Switch
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(R.string.settings_rotatable_tiles),
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.weight(1f)
                            )
                            Switch(
                                checked = tilesRotatable,
                                onCheckedChange = { viewModel.setPendingTilesRotatable(it) },
                                enabled = canRotateTiles
                            )
                        }

                        // Explanation for rotatable tiles
                        if (!canRotateTiles) {
                            Text(
                                text = stringResource(R.string.settings_rotatable_constraint),
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.secondary,
                                modifier = Modifier.padding(top = 8.dp)
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // Lock Percentage Settings Card
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Lock Percentage",
                            style = MaterialTheme.typography.titleMedium,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )

                        // Lock Percentage Slider
                        Column {
                            Text(
                                text = "Lock Percentage: ${lockPercent}%",
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )

                            Slider(
                                value = lockPercent.toFloat(),
                                onValueChange = { newValue ->
                                    viewModel.setPendingLockPercent(newValue.toInt())
                                },
                                valueRange = 0f..100f,
                                steps = 9, // This creates 10 steps (0, 10, 20, ..., 100)
                                modifier = Modifier.fillMaxWidth()
                            )

                            // Show the range labels
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = "0%",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.secondary
                                )
                                Text(
                                    text = "100%",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.secondary
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // Animation Settings Card
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.settings_animation),
                            style = MaterialTheme.typography.titleMedium,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )

                        // Animation Switch
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(R.string.settings_animate),
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.weight(1f)
                            )
                            Switch(
                                checked = animate,
                                onCheckedChange = { viewModel.setPendingAnimate(it) }
                            )
                        }

                        // Only show these options if animations are enabled
                        if (animate) {
                            // Animate Rotation Switch
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(start = 16.dp, top = 8.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = stringResource(R.string.settings_animate_rotation),
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.weight(1f)
                                )
                                Switch(
                                    checked = animateRotation,
                                    onCheckedChange = { viewModel.setPendingAnimateRotation(it) },
                                    enabled = animate
                                )
                            }

                            // Animate Swap Switch
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(start = 16.dp, top = 8.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = stringResource(R.string.settings_animate_swap),
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.weight(1f)
                                )
                                Switch(
                                    checked = animateSwap,
                                    onCheckedChange = { viewModel.setPendingAnimateSwap(it) },
                                    enabled = animate
                                )
                            }
                        }
                    }
                }

                // Add some space at the bottom for better appearance
                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
}
