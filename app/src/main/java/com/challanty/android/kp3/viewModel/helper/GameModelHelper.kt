package com.challanty.android.kp3.viewModel.helper

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Canvas
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.PaintingStyle
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import com.challanty.android.kp3.data.Settings
import com.challanty.android.kp3.data.repository.Repository
import com.challanty.android.kp3.pictureUnits.original.CelticPaths
import com.challanty.android.kp3.pictureUnits.original.CelticPicUnitFactory
import com.challanty.android.kp3.puzzle.CelticKnotPuzzle

import com.challanty.android.kp3.state.GameState
import com.challanty.android.kp3.viewModel.PicUnitDependencies
import com.challanty.android.kp3.viewModel.TileModel
import dagger.hilt.android.scopes.ViewModelScoped
import kotlinx.coroutines.CoroutineScope
import javax.inject.Inject
import kotlin.random.Random

@ViewModelScoped
class GameModelHelper @Inject constructor(
) {

    val ribbonPaint = Paint().apply {
        color = Color.Cyan // TODO: Make this a theme color
        style = PaintingStyle.Fill
        isAntiAlias = true
    }

    fun makeBoardGameState(
        puzzle: CelticKnotPuzzle,
        picUnitDependencies: PicUnitDependencies,
        settings: Settings,
    ): GameState {
        val picUnitSize = picUnitDependencies.picUnitSize
        val picUnitRows = picUnitDependencies.picUnitRows
        val picUnitCols = picUnitDependencies.picUnitCols

        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols

        val tileModelList: List<TileModel> = makeTileModels(
            puzzle = puzzle,
            picUnitDependencies = picUnitDependencies,
        )

        val boardPxSize = IntSize(
            width = (picUnitSize * picUnitCols).toInt(),
            height = (picUnitSize * picUnitRows).toInt()
        )

        val tilePxSize = IntSize(
            width = (picUnitSize * tileCols).toInt(),
            height = (picUnitSize * tileRows).toInt()
        )

        return GameState(
            showProgress = false,
            showGameBoard = true,
            showSolvedBoard = false,
            lockCnt = calcInitialLockCnt(settings),
            boardPxSize = boardPxSize,
            tilePxSize = tilePxSize,
            tileModels = tileModelList,
            solvedImage = null,
        )
    }

    fun makeWinGameState(
        puzzle: CelticKnotPuzzle,
        picUnitDependencies: PicUnitDependencies,
    ): GameState {
        val picUnitRows = picUnitDependencies.picUnitRows
        val picUnitCols = picUnitDependencies.picUnitCols
        val picUnitSize = picUnitDependencies.picUnitSize

        val boardPxSize = IntSize(
            width = (picUnitSize * picUnitCols).toInt(),
            height = (picUnitSize * picUnitRows).toInt()
        )

        val winBitmap = makeWinBitmap(
            puzzle = puzzle,
            boardPxSize = boardPxSize,
            picUnitDependencies = picUnitDependencies
        )

        val (tintColor, bgColor) = calcWinColors()

        return GameState(
            showProgress = false,
            showGameBoard = false,
            showSolvedBoard = true,
            boardPxSize = boardPxSize,
            solvedImage = winBitmap,
            solvedTintTargetColor = tintColor,
            solvedBGTargetColor = bgColor
        )
    }

    fun calcInitialLockCnt(settings: Settings): Int? {
        return if (settings.lockPercent == 0) {
            null
        } else {
            (settings.boardRows * settings.boardCols * settings.lockPercent / 100).toInt()
        }
    }

    private fun makeTileModels(
        puzzle: CelticKnotPuzzle,
        picUnitDependencies: PicUnitDependencies,
    ): List<TileModel> {
        val boardRows = puzzle.boardRows
        val boardCols = puzzle.boardCols
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols
        val picUnitSize = picUnitDependencies.picUnitSize
        val outlinePaint = picUnitDependencies.outlinePaint

        val tilePxSize = IntSize(
            width = (picUnitSize * tileCols).toInt(),
            height = (picUnitSize * tileRows).toInt()
        )

        // AI says the conventional way to locate a graphic is by its center and radius.
        val drawingRadius = picUnitSize / 2
        val drawingCenter = Offset(drawingRadius, drawingRadius)

        val tileModelList = mutableListOf<TileModel>()

        var tileID = 0

        for (boardRow in 0 until boardRows) {
            for (boardCol in 0 until boardCols) {

                val tileBitmap = ImageBitmap(
                    width = tilePxSize.width,
                    height = tilePxSize.height
                )
                val tileCanvas = Canvas(tileBitmap)

                for (tileRow in 0 until tileRows) {
                    val puzzleRow = boardRow * tileRows + tileRow

                    for (tileCol in 0 until tileCols) {
                        val puzzleCol = boardCol * tileCols + tileCol

                        val puzzlePicUnitID = puzzle.getScrambledPicUnitIDAt(
                            row = puzzleRow,
                            col = puzzleCol
                        )

                        val puzzlePicUnitStrategy =
                            CelticPicUnitFactory.createPicUnit(puzzlePicUnitID)
                        val puzzlePaths =
                            puzzlePicUnitStrategy.createPaths(drawingCenter, drawingRadius)

                        drawPicUnit(
                            canvas = tileCanvas,
                            paths = puzzlePaths,
                            x = tileCol * picUnitSize,
                            y = tileRow * picUnitSize,
                            ribbonPaint = ribbonPaint,
                            outlinePaint = outlinePaint
                        )
                    }
                }

                tileModelList.add(
                    TileModel(
                        id = tileID++,
                        bitmap = tileBitmap,
                        boardPosition = IntOffset(boardRow, boardCol),
                        initIntOffset = IntOffset.Zero,
                        initIntOffsetDuration = 0,
                        initQuarterTurnCnt = 0,
                        initRotationDuration = 0,
                        initIsLocked = false,
                        initIsSelected = false,
                    )
                )
            }
        }

        return tileModelList.toList()
    }

    private fun makeWinBitmap(
        puzzle: CelticKnotPuzzle,
        boardPxSize: IntSize,
        picUnitDependencies: PicUnitDependencies
    ): ImageBitmap {
        val picUnitSize = picUnitDependencies.picUnitSize

        val winBitmap = ImageBitmap(
            width = boardPxSize.width,
            height = boardPxSize.height
        )
        val winCanvas = Canvas(winBitmap)

        // AI says the conventional way to locate a graphic is by its center and radius.
        val drawingRadius = picUnitSize / 2
        val drawingCenter = Offset(drawingRadius, drawingRadius)

        for (puzzleRow in 0 until puzzle.rows) {
            for (puzzleCol in 0 until puzzle.cols) {
                val solvedPicUnitID = puzzle.getScrambledPicUnitIDAt(
                    row = puzzleRow,
                    col = puzzleCol
                )
                val solvedPicUnitStrategy = CelticPicUnitFactory.createPicUnit(solvedPicUnitID)
                val solvedPaths = solvedPicUnitStrategy.createPaths(drawingCenter, drawingRadius)

                drawPicUnit(
                    canvas = winCanvas,
                    paths = solvedPaths,
                    x = puzzleCol * picUnitSize,
                    y = puzzleRow * picUnitSize,
                    ribbonPaint = ribbonPaint,
                    outlinePaint = picUnitDependencies.outlinePaint
                )
            }
        }

        return winBitmap
    }

    private fun drawPicUnit(
        canvas: Canvas,
        paths: CelticPaths,
        x: Float,
        y: Float,
        ribbonPaint: Paint,
        outlinePaint: Paint
    ) {
        with(canvas) {
            save()
            translate(x, y)

            drawPath(paths.getRibbonPath(), ribbonPaint)
            drawPath(paths.getOutlinePath(), outlinePaint)

            restore()
        }
    }

    private fun calcWinColors(): Pair<Color, Color> {
        val r = Random.nextInt(0, 256)
        val g = Random.nextInt(0, 256)
        val b = Random.nextInt(0, 256)

        val color1 = Color(red = 255 - r, green = 255 - g, blue = 255 - b)
        val color2 = Color(red = r, green = g, blue = b)

        return Pair(color1, color2)
    }
}